import tailwind from "tailwindcss";
import postCss from "postcss";
import autoprefixer from "autoprefixer";
import cssnano from "cssnano";
import fs from "fs";
import path from "path";

export default async function (eleventyConfig) {
  eleventyConfig.addPassthroughCopy("./src/img");
  eleventyConfig.addWatchTarget("./src/css/");

  // Add global data for base URL
  eleventyConfig.addGlobalData("baseUrl", process.env.BASE_URL || "");

  return {
    passthroughFileCopy: true,
    markdownTemplateEngine: "njk",
    htmlTemplateEngine: "njk",
    dir: {
      input: "src",
      includes: "_includes",
      data: "_data",
      output: "public",
    },
  };
}
