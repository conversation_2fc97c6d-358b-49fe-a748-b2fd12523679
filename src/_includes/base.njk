<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="{{ description or 'Sanrado - IT solutions and software development for small and medium enterprises. Bootstrapped startup focused on digital transformation and consulting.' }}"
    />
    <meta
      name="keywords"
      content="IT solutions, software development, digital transformation, SME, consulting, {{ keywords or '' }}"
    />
    <meta name="author" content="Sanrado" />

    <!-- Open Graph -->
    <meta
      property="og:title"
      content="{{ title or 'Sanrado - IT Solutions for SMEs' }}"
    />
    <meta
      property="og:description"
      content="{{ description or 'Professional IT solutions and software development for small and medium enterprises.' }}"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="{{ url or 'https://sanrado.com' }}" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="{{ title or 'Sanrado - IT Solutions for SMEs' }}"
    />
    <meta
      name="twitter:description"
      content="{{ description or 'Professional IT solutions and software development for small and medium enterprises.' }}"
    />

    <title>{{ title or 'Sanrado - IT Solutions for SMEs' }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Atkinson+Hyperlegible:ital,wght@0,400;0,700;1,400;1,700&display=swap"
      rel="stylesheet"
    />

    <!-- Styles -->
    <link rel="stylesheet" href="{{ baseUrl }}/css/style.css" />

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Custom styles -->
    <style>
      .animate-on-scroll {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
      }

      .animate-on-scroll.animate {
        opacity: 1;
        transform: translateY(0);
      }

      .bg-gradient-primary {
        background: linear-gradient(135deg, #003b73 0%, #0074b7 100%);
      }

      .bg-gradient-secondary {
        background: linear-gradient(135deg, #0074b7 0%, #60a3d9 100%);
      }
    </style>
  </head>
  <body class="font-sans text-gray-900 bg-white">
    <!-- Navigation -->
    <nav
      class="fixed w-full top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-100 transition-all duration-300"
      id="navbar"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <a href="{{ baseUrl }}/" class="flex items-center space-x-3">
              <!-- Sanrado Logo -->
              <img src="{{ baseUrl }}/img/logo.svg" alt="Sanrado Logo" class="h-8 w-8" />
              <span class="text-xl font-display font-semibold text-primary"
                >Sanrado</span
              >
            </a>
          </div>

          <!-- Desktop Navigation -->
          <div class="hidden md:block">
            <div class="ml-10 flex items-baseline space-x-8">
              <a
                href="{{ baseUrl }}/"
                class="nav-link text-gray-700 hover:text-primary transition-colors duration-200 {% if page.url == '/' %}text-primary font-medium{% endif %}"
                >Home</a
              >
              <a
                href="{{ baseUrl }}/services/"
                class="nav-link text-gray-700 hover:text-primary transition-colors duration-200 {% if '/services/' in page.url %}text-primary font-medium{% endif %}"
                >Services</a
              >
              <a
                href="{{ baseUrl }}/projects/"
                class="nav-link text-gray-700 hover:text-primary transition-colors duration-200 {% if '/projects/' in page.url %}text-primary font-medium{% endif %}"
                >Projects</a
              >
              <a
                href="{{ baseUrl }}/about/"
                class="nav-link text-gray-700 hover:text-primary transition-colors duration-200 {% if '/about/' in page.url %}text-primary font-medium{% endif %}"
                >About</a
              >
              <a href="{{ baseUrl }}/contact/" class="btn-primary">Contact</a>
            </div>
          </div>

          <!-- Mobile menu button -->
          <div class="md:hidden">
            <button
              type="button"
              class="mobile-menu-btn bg-gray-50 inline-flex items-center justify-center p-2 rounded-md text-gray-600 hover:text-primary hover:bg-gray-100 transition-colors duration-200"
              aria-controls="mobile-menu"
              aria-expanded="false"
            >
              <span class="sr-only">Open main menu</span>
              <i data-lucide="menu" class="block h-6 w-6"></i>
              <i data-lucide="x" class="hidden h-6 w-6"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Mobile menu -->
      <div class="md:hidden mobile-menu hidden" id="mobile-menu">
        <div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-100">
          <a
            href="{{ baseUrl }}/"
            class="block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 {% if page.url == '/' %}text-primary bg-gray-50{% endif %}"
            >Home</a
          >
          <a
            href="{{ baseUrl }}/services/"
            class="block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 {% if '/services/' in page.url %}text-primary bg-gray-50{% endif %}"
            >Services</a
          >
          <a
            href="{{ baseUrl }}/projects/"
            class="block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 {% if '/projects/' in page.url %}text-primary bg-gray-50{% endif %}"
            >Projects</a
          >
          <a
            href="{{ baseUrl }}/about/"
            class="block px-3 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 rounded-md transition-colors duration-200 {% if '/about/' in page.url %}text-primary bg-gray-50{% endif %}"
            >About</a
          >
          <a href="{{ baseUrl }}/contact/" class="btn-primary block text-center">Contact</a>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
      {{ content | safe }}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Company Info -->
          <div class="col-span-1 md:col-span-2">
            <div class="flex items-center space-x-3 mb-4">
              <img src="{{ baseUrl }}/img/logo.svg" alt="Sanrado Logo" class="h-8 w-8" />
              <span class="text-xl font-display font-semibold">Sanrado</span>
            </div>
            <p class="text-gray-300 mb-4 max-w-md">
              Empowering small and medium enterprises with innovative IT
              solutions and digital transformation services.
            </p>

            <!-- Social Media Links -->
            <div class="flex space-x-4 mb-4">
              <a
                href="#"
                class="text-gray-400 hover:text-accent transition-colors duration-200"
              >
                <i data-lucide="linkedin" class="w-5 h-5"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-accent transition-colors duration-200"
              >
                <i data-lucide="twitter" class="w-5 h-5"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-accent transition-colors duration-200"
              >
                <i data-lucide="github" class="w-5 h-5"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-accent transition-colors duration-200"
              >
                <i data-lucide="mail" class="w-5 h-5"></i>
              </a>
            </div>

            <p class="text-gray-400 text-sm">
              © 2024 Sanrado. All rights reserved.
            </p>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li>
                <a
                  href="{{ baseUrl }}/"
                  class="text-gray-300 hover:text-accent transition-colors duration-200"
                  >Home</a
                >
              </li>
              <li>
                <a
                  href="{{ baseUrl }}/services/"
                  class="text-gray-300 hover:text-accent transition-colors duration-200"
                  >Services</a
                >
              </li>
              <li>
                <a
                  href="{{ baseUrl }}/projects/"
                  class="text-gray-300 hover:text-accent transition-colors duration-200"
                  >Projects</a
                >
              </li>
              <li>
                <a
                  href="{{ baseUrl }}/about/"
                  class="text-gray-300 hover:text-accent transition-colors duration-200"
                  >About</a
                >
              </li>
            </ul>
          </div>

          <!-- Contact Info -->
          <div>
            <h3 class="text-lg font-semibold mb-4">Contact</h3>
            <ul class="space-y-3 text-gray-300">
              <li class="flex items-center">
                <i data-lucide="mail" class="w-4 h-4 mr-2"></i>
                <EMAIL>
              </li>
              <li class="flex items-center">
                <i data-lucide="phone" class="w-4 h-4 mr-2"></i>
                +****************
              </li>
              <li class="flex items-center">
                <i data-lucide="map-pin" class="w-4 h-4 mr-2"></i>
                Bangalore, India
              </li>
            </ul>
          </div>
        </div>
      </div>
    </footer>

    <!-- JavaScript -->
    <script>
      // Mobile menu toggle
      const mobileMenuBtn = document.querySelector(".mobile-menu-btn");
      const mobileMenu = document.querySelector(".mobile-menu");
      const menuIcons = mobileMenuBtn.querySelectorAll("i");

      mobileMenuBtn.addEventListener("click", () => {
        mobileMenu.classList.toggle("hidden");
        menuIcons.forEach((icon) => icon.classList.toggle("hidden"));
      });

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });

      // Scroll animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate");
          }
        });
      }, observerOptions);

      // Observe all elements with animate-on-scroll class
      document.querySelectorAll(".animate-on-scroll").forEach((el) => {
        observer.observe(el);
      });

      // Navbar scroll effect
      let lastScrollTop = 0;
      const navbar = document.getElementById("navbar");

      window.addEventListener("scroll", () => {
        const scrollTop =
          window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
          navbar.classList.add("shadow-lg", "bg-white");
          navbar.classList.remove("bg-white/95");
        } else {
          navbar.classList.remove("shadow-lg", "bg-white");
          navbar.classList.add("bg-white/95");
        }

        lastScrollTop = scrollTop;
      });

      // Initialize Lucide icons
      lucide.createIcons();
    </script>
  </body>
</html>
