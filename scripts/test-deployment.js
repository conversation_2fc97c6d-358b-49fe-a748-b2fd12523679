#!/usr/bin/env node

/**
 * Test script to verify deployment readiness
 * Run with: node scripts/test-deployment.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

console.log('🔍 Testing Sanrado Website Deployment Readiness\n');

// Test 1: Check required files exist
console.log('1. Checking required files...');
const requiredFiles = [
  '.github/workflows/deploy.yml',
  'package.json',
  'eleventy.config.js',
  'src/_includes/base.njk',
  'public/index.html',
  'public/css/style.css'
];

let allFilesExist = true;
requiredFiles.forEach(file => {
  const filePath = path.join(rootDir, file);
  if (fs.existsSync(filePath)) {
    console.log(`   ✅ ${file}`);
  } else {
    console.log(`   ❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

// Test 2: Check package.json scripts
console.log('\n2. Checking package.json scripts...');
const packageJson = JSON.parse(fs.readFileSync(path.join(rootDir, 'package.json'), 'utf8'));
const requiredScripts = ['build', 'build:css', 'build:prod', 'clean'];

requiredScripts.forEach(script => {
  if (packageJson.scripts[script]) {
    console.log(`   ✅ ${script}: ${packageJson.scripts[script]}`);
  } else {
    console.log(`   ❌ ${script} - MISSING`);
    allFilesExist = false;
  }
});

// Test 3: Check GitHub Actions workflow
console.log('\n3. Checking GitHub Actions workflow...');
const workflowPath = path.join(rootDir, '.github/workflows/deploy.yml');
if (fs.existsSync(workflowPath)) {
  const workflow = fs.readFileSync(workflowPath, 'utf8');
  const checks = [
    { name: 'Node.js setup', pattern: /setup-node@v4/ },
    { name: 'Build command', pattern: /npm run build/ },
    { name: 'Pages deployment', pattern: /deploy-pages@v4/ },
    { name: 'Artifact upload', pattern: /upload-pages-artifact@v3/ }
  ];

  checks.forEach(check => {
    if (check.pattern.test(workflow)) {
      console.log(`   ✅ ${check.name}`);
    } else {
      console.log(`   ❌ ${check.name} - NOT FOUND`);
      allFilesExist = false;
    }
  });
} else {
  console.log('   ❌ Workflow file missing');
  allFilesExist = false;
}

// Test 4: Check build output
console.log('\n4. Checking build output...');
const publicDir = path.join(rootDir, 'public');
if (fs.existsSync(publicDir)) {
  const files = fs.readdirSync(publicDir);
  const expectedFiles = ['index.html', 'css', 'img'];
  
  expectedFiles.forEach(file => {
    if (files.includes(file)) {
      console.log(`   ✅ ${file}`);
    } else {
      console.log(`   ❌ ${file} - MISSING`);
      allFilesExist = false;
    }
  });
} else {
  console.log('   ❌ Public directory missing - run npm run build');
  allFilesExist = false;
}

// Test 5: Check base URL configuration
console.log('\n5. Checking base URL configuration...');
const indexHtml = fs.readFileSync(path.join(rootDir, 'public/index.html'), 'utf8');
if (indexHtml.includes('href="/css/style.css"') || indexHtml.includes('href="{{ baseUrl }}/css/style.css"')) {
  console.log('   ✅ Base URL configuration detected');
} else {
  console.log('   ❌ Base URL configuration not found');
  allFilesExist = false;
}

// Final result
console.log('\n' + '='.repeat(50));
if (allFilesExist) {
  console.log('🎉 DEPLOYMENT READY!');
  console.log('\nNext steps:');
  console.log('1. Create GitHub repository');
  console.log('2. Push code to GitHub');
  console.log('3. Enable GitHub Pages with Actions source');
  console.log('4. Monitor deployment in Actions tab');
} else {
  console.log('❌ DEPLOYMENT NOT READY');
  console.log('\nPlease fix the issues above before deploying.');
}
console.log('='.repeat(50));
