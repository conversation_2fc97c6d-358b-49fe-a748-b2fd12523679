# 🚀 GitHub Pages Deployment Guide

This guide provides step-by-step instructions for deploying the Sanrado website to GitHub Pages using automated GitHub Actions.

## 📋 Prerequisites

- GitHub account
- Git installed locally
- Node.js 18+ and npm/pnpm
- Repository with the Sanrado website code

## 🔧 Setup Instructions

### 1. Create GitHub Repository

1. **Create a new repository** on GitHub:
   - Repository name: `sanrado-com` (or your preferred name)
   - Set to **Public** (required for free GitHub Pages)
   - Don't initialize with README (since you have existing code)

2. **Connect your local repository**:
   ```bash
   git remote add origin https://github.com/YOUR_USERNAME/sanrado-com.git
   git branch -M main
   git push -u origin main
   ```

### 2. Configure GitHub Pages

1. **Go to repository Settings** → **Pages**
2. **Source**: Select "GitHub Actions"
3. **Custom domain** (optional): Add your domain if you have one

### 3. Environment Variables (Optional)

For custom domain or subdirectory deployment:

1. **Repository Settings** → **Secrets and variables** → **Actions**
2. **Add repository variable**:
   - Name: `BASE_URL`
   - Value: `/your-repo-name` (for subdirectory) or empty for root domain

### 4. Deploy

1. **Push your code**:
   ```bash
   git add .
   git commit -m "Setup GitHub Pages deployment"
   git push origin main
   ```

2. **Monitor deployment**:
   - Go to **Actions** tab in your repository
   - Watch the "Deploy to GitHub Pages" workflow
   - Deployment typically takes 2-3 minutes

3. **Access your site**:
   - **Default URL**: `https://YOUR_USERNAME.github.io/sanrado-com/`
   - **Custom domain**: Your configured domain

## 🛠️ Technical Implementation

### GitHub Actions Workflow

The deployment uses a two-job workflow:

**Build Job:**
- Checks out code
- Sets up Node.js 20
- Installs dependencies with `npm ci`
- Builds the site with `npm run build`
- Uploads build artifacts

**Deploy Job:**
- Downloads build artifacts
- Deploys to GitHub Pages
- Updates the live site

### Build Process

1. **CSS Processing**: PostCSS processes TailwindCSS with autoprefixer and cssnano
2. **Static Generation**: Eleventy generates HTML from Nunjucks templates
3. **Asset Copying**: Images and other assets are copied to output directory
4. **Base URL Handling**: Dynamic base URL support for subdirectory deployment

### File Structure

```
.github/
└── workflows/
    └── deploy.yml          # GitHub Actions workflow

public/                     # Build output (now tracked in git)
├── css/
├── img/
├── index.html
└── ...

src/                        # Source files
├── _includes/
├── css/
├── img/
└── *.njk
```

## 🔍 Troubleshooting

### Common Issues

**1. Build Fails**
- Check Node.js version (requires 18+)
- Verify all dependencies are in package.json
- Check for syntax errors in templates

**2. Assets Not Loading**
- Verify base URL configuration
- Check asset paths in templates use `{{ baseUrl }}`
- Ensure images are in `src/img/` directory

**3. 404 Errors**
- Check repository is public
- Verify GitHub Pages source is set to "GitHub Actions"
- Confirm deployment completed successfully

**4. Custom Domain Issues**
- Add CNAME file to `public/` directory
- Configure DNS records properly
- Allow 24-48 hours for DNS propagation

### Debug Commands

```bash
# Test build locally
npm run build

# Check output directory
ls -la public/

# Verify CSS generation
npm run build:css

# Clean and rebuild
npm run clean && npm run build
```

## 📈 Performance Optimization

### Implemented Optimizations

- **CSS Minification**: cssnano reduces CSS file size
- **Asset Optimization**: Proper image formats and compression
- **Caching**: GitHub Pages provides CDN caching
- **Clean URLs**: Eleventy generates SEO-friendly URLs

### Monitoring

- **GitHub Actions**: Monitor build times and success rates
- **Lighthouse**: Regular performance audits
- **Analytics**: Consider adding Google Analytics or similar

## 🔄 Maintenance

### Regular Updates

1. **Dependencies**: Update npm packages monthly
2. **Content**: Update through direct file editing or CMS
3. **Monitoring**: Check deployment status after each push

### Backup Strategy

- **Git History**: Full version control
- **Branch Protection**: Consider protecting main branch
- **Local Backups**: Regular local repository backups

## 🎯 Next Steps

1. **Custom Domain**: Configure your own domain
2. **Analytics**: Add tracking and monitoring
3. **SEO**: Optimize meta tags and structured data
4. **Performance**: Regular Lighthouse audits
5. **Security**: HTTPS enforcement and security headers

## 📞 Support

For issues with this deployment setup:
1. Check GitHub Actions logs
2. Review this documentation
3. Check Eleventy documentation
4. GitHub Pages documentation
