{"name": "sanrado-com", "version": "1.0.0", "description": "Professional website for Sanrado - IT solutions for SMEs", "main": "index.js", "type": "module", "scripts": {"build:css": "postcss src/css/style.css -o public/css/style.css", "dev": "npm run build:css && npx @11ty/eleventy --serve", "build": "npm run build:css && npx @11ty/eleventy", "build:prod": "npm run clean && npm run build", "clean": "rm -rf public", "start": "npm run dev", "deploy": "npm run build:prod", "test:deploy": "node scripts/test-deployment.js"}, "keywords": ["11ty", "eleventy", "tailwindcss", "static-site"], "author": "<PERSON>", "license": "ISC", "packageManager": "pnpm@10.12.1", "dependencies": {"@11ty/eleventy": "^3.1.2", "@tailwindcss/postcss": "^4.1.11", "autoprefixer": "^10.4.21", "cssnano": "^7.0.7", "lucide": "^0.523.0", "postcss": "^8.5.6", "postcss-cli": "^11.0.1", "tailwindcss": "^4.1.11"}}